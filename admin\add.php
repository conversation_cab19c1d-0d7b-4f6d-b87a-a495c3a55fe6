<?php
/**
 * Admin Course Management
 *
 * Add, edit, and manage courses
 */

require_once '../config.php';

// Require admin access
requireAdmin();

$action = $_GET['action'] ?? 'list';
$courseId = intval($_GET['id'] ?? 0);
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add' || $action === 'edit') {
        $title = sanitizeInput($_POST['title'] ?? '');
        $description = sanitizeInput($_POST['description'] ?? '');
        $category = sanitizeInput($_POST['category'] ?? '');
        $level = sanitizeInput($_POST['level'] ?? '');
        $isFeatured = isset($_POST['is_featured']) ? 1 : 0;

        if (empty($title) || empty($category) || empty($level)) {
            $error = 'Veuillez remplir tous les champs obligatoires.';
        } else {
            try {
                if ($action === 'add') {
                    executeQuery($pdo, "
                        INSERT INTO courses (title, description, category, level, is_featured, instructor_id)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ", [$title, $description, $category, $level, $isFeatured, $_SESSION['user_id']]);
                    $message = 'Cours ajouté avec succès!';
                } else {
                    executeQuery($pdo, "
                        UPDATE courses
                        SET title = ?, description = ?, category = ?, level = ?, is_featured = ?
                        WHERE id = ?
                    ", [$title, $description, $category, $level, $isFeatured, $courseId]);
                    $message = 'Cours modifié avec succès!';
                }
            } catch (Exception $e) {
                $error = 'Erreur lors de la sauvegarde: ' . $e->getMessage();
            }
        }
    } elseif ($action === 'delete' && $courseId > 0) {
        try {
            executeQuery($pdo, "DELETE FROM courses WHERE id = ?", [$courseId]);

            $message = 'Cours supprimé avec succès!';
            $action = 'list';
        } catch (Exception $e) {
            $error = 'Erreur lors de la suppression: ' . $e->getMessage();
        }
    }
}

// Handle GET delete action (from JavaScript)
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $courseId = intval($_GET['id']);
    if ($courseId > 0) {
        try {
            executeQuery($pdo, "DELETE FROM courses WHERE id = ?", [$courseId]);
            $message = 'Cours supprimé avec succès!';
        } catch (Exception $e) {
            $error = 'Erreur lors de la suppression: ' . $e->getMessage();
        }
    }
    $action = 'list';
}

// Get course data for editing
$course = null;
if ($action === 'edit' && $courseId > 0) {
    $course = getCourseById($pdo, $courseId);
    if (!$course) {
        $error = 'Cours introuvable.';
        $action = 'list';
    }
}

// Get all courses for listing
$courses = [];
if ($action === 'list') {
    try {
        $stmt = executeQuery($pdo, "
            SELECT c.*, COUNT(l.id) as lesson_count,
                   u.first_name, u.last_name
            FROM courses c
            LEFT JOIN lessons l ON c.id = l.course_id
            LEFT JOIN users u ON c.instructor_id = u.id
            GROUP BY c.id
            ORDER BY c.created_at DESC
        ");
        $courses = $stmt->fetchAll();
    } catch (Exception $e) {
        $error = 'Erreur lors du chargement des cours.';
    }
}

$pageTitle = 'Gestion des cours - Administration';
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Admin Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="dashboard.php" class="text-blue-600 hover:text-blue-800 mr-4">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <h1 class="text-2xl font-bold text-gray-800">
                        <?= $action === 'add' ? 'Nouveau cours' : ($action === 'edit' ? 'Modifier le cours' : 'Gestion des cours') ?>
                    </h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="../index.php" class="text-gray-600 hover:text-blue-600 transition-colors">
                        <i class="fas fa-home mr-1"></i> Site
                    </a>
                    <a href="../auth/logout.php" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i> Déconnexion
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar -->
        <aside class="w-64 bg-white shadow-sm min-h-screen">
            <nav class="p-6">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-tachometer-alt mr-3"></i>
                            Tableau de bord
                        </a>
                    </li>
                    <li>
                        <a href="add.php" class="flex items-center px-4 py-2 text-blue-600 bg-blue-50 rounded-lg font-medium">
                            <i class="fas fa-book mr-3"></i>
                            Cours
                        </a>
                    </li>
                    <li>
                        <a href="lessons.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-file-alt mr-3"></i>
                            Leçons
                        </a>
                    </li>
                    <li>
                        <a href="users.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-users mr-3"></i>
                            Utilisateurs
                        </a>
                    </li>
                    <li>
                        <a href="upload.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-upload mr-3"></i>
                            Upload fichiers
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6">
            <!-- Messages -->
            <?php if ($message): ?>
                <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
                    <p class="text-green-600"><?= htmlspecialchars($message) ?></p>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                    <p class="text-red-600"><?= htmlspecialchars($error) ?></p>
                </div>
            <?php endif; ?>

            <?php if ($action === 'list'): ?>
                <!-- Courses List -->
                <div class="bg-white rounded-xl shadow-lg">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h2 class="text-xl font-bold text-gray-800">Tous les cours</h2>
                            <a href="?action=add" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                <i class="fas fa-plus mr-2"></i>
                                Nouveau cours
                            </a>
                        </div>
                    </div>

                    <?php if (empty($courses)): ?>
                        <div class="p-12 text-center">
                            <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-book text-gray-400 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-800 mb-2">Aucun cours</h3>
                            <p class="text-gray-600 mb-4">Commencez par créer votre premier cours.</p>
                            <a href="?action=add" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                                Créer un cours
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cours</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Catégorie</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Niveau</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Leçons</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php foreach ($courses as $course): ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">
                                                        <?= htmlspecialchars($course['title']) ?>
                                                    </div>
                                                    <div class="text-sm text-gray-500">
                                                        <?= htmlspecialchars(substr($course['description'], 0, 60)) ?>...
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    <?= htmlspecialchars($course['category']) ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?= htmlspecialchars($course['level']) ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?= $course['lesson_count'] ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php if ($course['is_featured']): ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                        <i class="fas fa-star mr-1"></i>Vedette
                                                    </span>
                                                <?php else: ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                        Normal
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div class="flex space-x-2">
                                                    <a href="../course.php?id=<?= $course['id'] ?>" target="_blank" class="text-blue-600 hover:text-blue-900">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="?action=edit&id=<?= $course['id'] ?>" class="text-indigo-600 hover:text-indigo-900">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button onclick="deleteCourse(<?= $course['id'] ?>)" class="text-red-600 hover:text-red-900">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>

            <?php elseif ($action === 'add' || $action === 'edit'): ?>
                <!-- Course Form -->
                <div class="bg-white rounded-xl shadow-lg p-8">
                    <form method="POST" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                    Titre du cours *
                                </label>
                                <input
                                    type="text"
                                    id="title"
                                    name="title"
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    value="<?= htmlspecialchars($course['title'] ?? '') ?>"
                                >
                            </div>

                            <div>
                                <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                                    Catégorie *
                                </label>
                                <select
                                    id="category"
                                    name="category"
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                >
                                    <option value="">Sélectionner une catégorie</option>
                                    <option value="Physique" <?= ($course['category'] ?? '') === 'Physique' ? 'selected' : '' ?>>Physique</option>
                                    <option value="Chimie" <?= ($course['category'] ?? '') === 'Chimie' ? 'selected' : '' ?>>Chimie</option>
                                    <option value="Mathématiques" <?= ($course['category'] ?? '') === 'Mathématiques' ? 'selected' : '' ?>>Mathématiques</option>
                                </select>
                            </div>

                            <div>
                                <label for="level" class="block text-sm font-medium text-gray-700 mb-2">
                                    Niveau *
                                </label>
                                <select
                                    id="level"
                                    name="level"
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                >
                                    <option value="">Sélectionner un niveau</option>
                                    <option value="1AC" <?= ($course['level'] ?? '') === '1AC' ? 'selected' : '' ?>>1ère Année Collège</option>
                                    <option value="2AC" <?= ($course['level'] ?? '') === '2AC' ? 'selected' : '' ?>>2ème Année Collège</option>
                                    <option value="3AC" <?= ($course['level'] ?? '') === '3AC' ? 'selected' : '' ?>>3ème Année Collège</option>
                                    <option value="Tronc Commun" <?= ($course['level'] ?? '') === 'Tronc Commun' ? 'selected' : '' ?>>Tronc Commun</option>
                                    <option value="1ère Bac" <?= ($course['level'] ?? '') === '1ère Bac' ? 'selected' : '' ?>>1ère Bac</option>
                                    <option value="2ème Bac" <?= ($course['level'] ?? '') === '2ème Bac' ? 'selected' : '' ?>>2ème Bac</option>
                                </select>
                            </div>

                            <div class="flex items-center">
                                <input
                                    type="checkbox"
                                    id="is_featured"
                                    name="is_featured"
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    <?= ($course['is_featured'] ?? 0) ? 'checked' : '' ?>
                                >
                                <label for="is_featured" class="ml-2 block text-sm text-gray-900">
                                    Cours en vedette
                                </label>
                            </div>
                        </div>

                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                Description
                            </label>
                            <textarea
                                id="description"
                                name="description"
                                rows="4"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="Description détaillée du cours..."
                            ><?= htmlspecialchars($course['description'] ?? '') ?></textarea>
                        </div>

                        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                            <a href="?action=list" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Retour
                            </a>
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                                <i class="fas fa-save mr-2"></i>
                                <?= $action === 'add' ? 'Créer le cours' : 'Modifier le cours' ?>
                            </button>
                        </div>
                    </form>
                </div>
            <?php endif; ?>
        </main>
    </div>

    <script>
        function deleteCourse(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer ce cours ? Cette action est irréversible.\n\nToutes les leçons associées seront également supprimées.')) {
                // Show loading state
                const button = event.target.closest('button');
                if (button) {
                    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    button.disabled = true;
                }

                // Redirect to delete
                window.location.href = '?action=delete&id=' + id;
            }
        }
    </script>
</body>
</html>
