<?php
/**
 * Admin Dashboard
 * 
 * Main admin panel with overview and quick actions
 */

require_once '../config.php';

// Require admin access
requireAdmin();

// Get dashboard statistics
try {
    $stats = executeQuery($pdo, "
        SELECT 
            (SELECT COUNT(*) FROM courses) as total_courses,
            (SELECT COUNT(*) FROM lessons) as total_lessons,
            (SELECT COUNT(*) FROM users WHERE role = 'student') as total_students,
            (SELECT COUNT(*) FROM users WHERE role = 'admin') as total_admins,
            (SELECT COUNT(*) FROM courses WHERE is_featured = 1) as featured_courses
    ")->fetch();

    // Get recent courses
    $recentCourses = executeQuery($pdo, "
        SELECT c.*, COUNT(l.id) as lesson_count 
        FROM courses c 
        LEFT JOIN lessons l ON c.id = l.course_id 
        GROUP BY c.id 
        ORDER BY c.created_at DESC 
        LIMIT 5
    ")->fetchAll();

    // Get recent users
    $recentUsers = executeQuery($pdo, "
        SELECT * FROM users 
        ORDER BY created_at DESC 
        LIMIT 5
    ")->fetchAll();

} catch (Exception $e) {
    $stats = ['total_courses' => 0, 'total_lessons' => 0, 'total_students' => 0, 'total_admins' => 0, 'featured_courses' => 0];
    $recentCourses = [];
    $recentUsers = [];
    error_log("Dashboard error: " . $e->getMessage());
}

$pageTitle = 'Tableau de bord - Administration';
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Admin Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-800">Administration</h1>
                    <span class="ml-4 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                        <?= htmlspecialchars($_SESSION['user_name']) ?>
                    </span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="../index.php" class="text-gray-600 hover:text-blue-600 transition-colors">
                        <i class="fas fa-home mr-1"></i> Site
                    </a>
                    <a href="../auth/logout.php" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i> Déconnexion
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar -->
        <aside class="w-64 bg-white shadow-sm min-h-screen">
            <nav class="p-6">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center px-4 py-2 text-blue-600 bg-blue-50 rounded-lg font-medium">
                            <i class="fas fa-tachometer-alt mr-3"></i>
                            Tableau de bord
                        </a>
                    </li>
                    <li>
                        <a href="add.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-book mr-3"></i>
                            Cours
                        </a>
                    </li>
                    <li>
                        <a href="lessons.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-file-alt mr-3"></i>
                            Leçons
                        </a>
                    </li>
                    <li>
                        <a href="users.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-users mr-3"></i>
                            Utilisateurs
                        </a>
                    </li>
                    <li>
                        <a href="upload.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-upload mr-3"></i>
                            Upload fichiers
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6">
            <!-- Welcome Section -->
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">Bienvenue, <?= htmlspecialchars($_SESSION['user_name']) ?>!</h2>
                <p class="text-gray-600">Voici un aperçu de votre plateforme d'apprentissage.</p>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-book text-blue-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-gray-600 text-sm">Total Cours</p>
                            <p class="text-2xl font-bold text-gray-800"><?= number_format($stats['total_courses']) ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-file-alt text-green-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-gray-600 text-sm">Total Leçons</p>
                            <p class="text-2xl font-bold text-gray-800"><?= number_format($stats['total_lessons']) ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-purple-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-gray-600 text-sm">Étudiants</p>
                            <p class="text-2xl font-bold text-gray-800"><?= number_format($stats['total_students']) ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-star text-yellow-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-gray-600 text-sm">Cours vedettes</p>
                            <p class="text-2xl font-bold text-gray-800"><?= number_format($stats['featured_courses']) ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">Actions rapides</h3>
                    <div class="space-y-3">
                        <a href="add.php?action=add" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-center block">
                            <i class="fas fa-plus mr-2"></i>
                            Nouveau cours
                        </a>
                        <a href="upload.php" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-center block">
                            <i class="fas fa-upload mr-2"></i>
                            Upload fichier
                        </a>
                        <a href="users.php" class="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-center block">
                            <i class="fas fa-users mr-2"></i>
                            Gérer utilisateurs
                        </a>
                    </div>
                </div>

                <!-- Recent Courses -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">Cours récents</h3>
                    <?php if (!empty($recentCourses)): ?>
                        <div class="space-y-3">
                            <?php foreach ($recentCourses as $course): ?>
                                <div class="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                                    <div>
                                        <p class="font-medium text-gray-800 text-sm"><?= htmlspecialchars($course['title']) ?></p>
                                        <p class="text-gray-500 text-xs"><?= htmlspecialchars($course['category']) ?> • <?= $course['lesson_count'] ?> leçons</p>
                                    </div>
                                    <a href="courses.php?action=edit&id=<?= $course['id'] ?>" class="text-blue-600 hover:text-blue-800">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-gray-500 text-sm">Aucun cours disponible</p>
                    <?php endif; ?>
                </div>

                <!-- Recent Users -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">Utilisateurs récents</h3>
                    <?php if (!empty($recentUsers)): ?>
                        <div class="space-y-3">
                            <?php foreach ($recentUsers as $user): ?>
                                <div class="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                                    <div>
                                        <p class="font-medium text-gray-800 text-sm"><?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?></p>
                                        <p class="text-gray-500 text-xs"><?= htmlspecialchars($user['email']) ?> • <?= ucfirst($user['role']) ?></p>
                                    </div>
                                    <span class="<?= $user['role'] === 'admin' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800' ?> px-2 py-1 rounded-full text-xs font-medium">
                                        <?= ucfirst($user['role']) ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-gray-500 text-sm">Aucun utilisateur</p>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
