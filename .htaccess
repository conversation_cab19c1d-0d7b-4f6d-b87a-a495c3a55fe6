# AdrarPhysic E-Learning Platform - Apache Configuration
# Security, Performance, and SEO optimizations

# Enable rewrite engine
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevent MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # Enable XSS protection
    Header set X-XSS-Protection "1; mode=block"
    
    # Referrer policy
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (basic)
    Header set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data:; img-src 'self' https: data:; font-src 'self' https: data:;"
</IfModule>

# Hide sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
</IfModule>

# Cache control
<IfModule mod_headers.c>
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|svg|pdf)$">
        Header set Cache-Control "max-age=2592000, public"
    </FilesMatch>
</IfModule>

# Pretty URLs (if needed)
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule ^course/([0-9]+)/?$ course.php?id=$1 [L,QSA]
# RewriteRule ^courses/([a-zA-Z0-9-]+)/?$ courses.php?category=$1 [L,QSA]

# Force HTTPS (uncomment if using SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remove trailing slashes
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{THE_REQUEST} /+[^?\s]*?/[\s?]
RewriteRule ^(.+)/$ /$1 [R=301,L]

# Error pages
ErrorDocument 404 /404.php
ErrorDocument 500 /500.php

# Prevent hotlinking
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?yourdomain.com [NC]
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?localhost [NC]
RewriteRule \.(jpg|jpeg|png|gif|svg)$ - [NC,F,L]

# PHP settings
<IfModule mod_php.c>
    # Hide PHP version
    php_flag expose_php off
    
    # Increase upload limits
    php_value upload_max_filesize 50M
    php_value post_max_size 50M
    php_value max_execution_time 300
    php_value max_input_time 300
    
    # Session security
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 0
    php_value session.use_only_cookies 1
</IfModule>
