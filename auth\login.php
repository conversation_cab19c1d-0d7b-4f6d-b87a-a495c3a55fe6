<?php
/**
 * Login Page
 * 
 * Handles user authentication and login functionality
 */

require_once '../config.php';

$error = '';
$success = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        $error = 'Veuillez remplir tous les champs.';
    } else {
        try {
            // Check if user exists
            $stmt = executeQuery($pdo, "SELECT * FROM users WHERE email = ?", [$email]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password'])) {
                // Login successful
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['user_role'] = $user['role'];
                $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
                
                // Redirect based on role
                if ($user['role'] === 'admin') {
                    header('Location: ../admin/dashboard.php');
                } else {
                    header('Location: ../index.php');
                }
                exit;
            } else {
                $error = 'Email ou mot de passe incorrect.';
            }
        } catch (Exception $e) {
            $error = 'Erreur de connexion. Veuillez réessayer.';
            error_log("Login error: " . $e->getMessage());
        }
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - <?= APP_NAME ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <h2 class="mt-6 text-3xl font-bold text-gray-900">
                    Connexion à votre compte
                </h2>
                <p class="mt-2 text-sm text-gray-600">
                    Ou 
                    <a href="register.php" class="font-medium text-indigo-600 hover:text-indigo-500">
                        créez un nouveau compte
                    </a>
                </p>
            </div>

            <!-- Login Form -->
            <form class="mt-8 space-y-6" method="POST">
                <div class="bg-white p-8 rounded-xl shadow-lg">
                    <?php if ($error): ?>
                        <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
                            <p class="text-red-600 text-sm"><?= htmlspecialchars($error) ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                        <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
                            <p class="text-green-600 text-sm"><?= htmlspecialchars($success) ?></p>
                        </div>
                    <?php endif; ?>

                    <div class="space-y-4">
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700">
                                Adresse email
                            </label>
                            <input 
                                id="email" 
                                name="email" 
                                type="email" 
                                required 
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                placeholder="<EMAIL>"
                                value="<?= htmlspecialchars($email ?? '') ?>"
                            >
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700">
                                Mot de passe
                            </label>
                            <input 
                                id="password" 
                                name="password" 
                                type="password" 
                                required 
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                placeholder="Votre mot de passe"
                            >
                        </div>
                    </div>

                    <div class="mt-6">
                        <button 
                            type="submit" 
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200"
                        >
                            Se connecter
                        </button>
                    </div>

                    <div class="mt-4 text-center">
                        <a href="../index.php" class="text-sm text-indigo-600 hover:text-indigo-500">
                            ← Retour à l'accueil
                        </a>
                    </div>
                </div>
            </form>

            <!-- Demo credentials -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mt-4">
                <h3 class="text-sm font-medium text-yellow-800">Comptes de démonstration:</h3>
                <p class="text-xs text-yellow-700 mt-1">
                    <strong>Admin:</strong> <EMAIL> / admin123<br>
                    <strong>Étudiant:</strong> Créez un nouveau compte
                </p>
            </div>
        </div>
    </div>
</body>
</html>
