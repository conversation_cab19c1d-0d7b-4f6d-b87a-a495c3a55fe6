<?php
/**
 * Admin Lessons Management
 * 
 * View and manage lessons
 */

require_once '../config.php';

// Require admin access
requireAdmin();

$message = '';
$error = '';

// Handle lesson actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $lessonId = intval($_POST['lesson_id'] ?? 0);
    
    if ($action === 'delete' && $lessonId > 0) {
        try {
            // Get lesson file path before deletion
            $lesson = executeQuery($pdo, "SELECT file_path FROM lessons WHERE id = ?", [$lessonId])->fetch();
            
            // Delete from database
            executeQuery($pdo, "DELETE FROM lessons WHERE id = ?", [$lessonId]);
            
            // Delete file if exists
            if ($lesson && $lesson['file_path'] && file_exists($lesson['file_path'])) {
                unlink($lesson['file_path']);
            }
            
            $message = 'Leçon supprimée avec succès.';
        } catch (Exception $e) {
            $error = 'Erreur lors de la suppression: ' . $e->getMessage();
        }
    }
}

// Get all lessons with course information
try {
    $lessons = executeQuery($pdo, "
        SELECT l.*, c.title as course_title, c.category, c.level
        FROM lessons l 
        JOIN courses c ON l.course_id = c.id 
        ORDER BY c.title, l.lesson_order, l.created_at DESC
    ")->fetchAll();
} catch (Exception $e) {
    $lessons = [];
    $error = 'Erreur lors du chargement des leçons.';
}

$pageTitle = 'Gestion des leçons - Administration';
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Admin Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="dashboard.php" class="text-blue-600 hover:text-blue-800 mr-4">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <h1 class="text-2xl font-bold text-gray-800">Gestion des leçons</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="../index.php" class="text-gray-600 hover:text-blue-600 transition-colors">
                        <i class="fas fa-home mr-1"></i> Site
                    </a>
                    <a href="../auth/logout.php" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i> Déconnexion
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar -->
        <aside class="w-64 bg-white shadow-sm min-h-screen">
            <nav class="p-6">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-tachometer-alt mr-3"></i>
                            Tableau de bord
                        </a>
                    </li>
                    <li>
                        <a href="add.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-book mr-3"></i>
                            Cours
                        </a>
                    </li>
                    <li>
                        <a href="lessons.php" class="flex items-center px-4 py-2 text-blue-600 bg-blue-50 rounded-lg font-medium">
                            <i class="fas fa-file-alt mr-3"></i>
                            Leçons
                        </a>
                    </li>
                    <li>
                        <a href="users.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-users mr-3"></i>
                            Utilisateurs
                        </a>
                    </li>
                    <li>
                        <a href="upload.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-upload mr-3"></i>
                            Upload fichiers
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6">
            <!-- Messages -->
            <?php if ($message): ?>
                <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
                    <p class="text-green-600"><?= htmlspecialchars($message) ?></p>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                    <p class="text-red-600"><?= htmlspecialchars($error) ?></p>
                </div>
            <?php endif; ?>

            <!-- Lessons Table -->
            <div class="bg-white rounded-xl shadow-lg">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-bold text-gray-800">Toutes les leçons (<?= count($lessons) ?>)</h2>
                        <a href="upload.php" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            Nouvelle leçon
                        </a>
                    </div>
                </div>
                
                <?php if (empty($lessons)): ?>
                    <div class="p-12 text-center">
                        <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-file-alt text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">Aucune leçon</h3>
                        <p class="text-gray-600 mb-4">Commencez par uploader votre première leçon.</p>
                        <a href="upload.php" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Uploader une leçon
                        </a>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Leçon</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cours</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ordre</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Créé le</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($lessons as $lesson): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?= htmlspecialchars($lesson['title']) ?>
                                                </div>
                                                <?php if ($lesson['description']): ?>
                                                    <div class="text-sm text-gray-500">
                                                        <?= htmlspecialchars(substr($lesson['description'], 0, 60)) ?>...
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?= htmlspecialchars($lesson['course_title']) ?>
                                                </div>
                                                <div class="text-sm text-gray-500">
                                                    <?= htmlspecialchars($lesson['category']) ?> - <?= htmlspecialchars($lesson['level']) ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                <?= strtoupper($lesson['file_type']) ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?= $lesson['lesson_order'] ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?= date('d/m/Y', strtotime($lesson['created_at'])) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <?php if ($lesson['file_path']): ?>
                                                    <a href="../<?= htmlspecialchars($lesson['file_path']) ?>" target="_blank" class="text-blue-600 hover:text-blue-900">
                                                        <i class="fas fa-external-link-alt" title="Ouvrir le fichier"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <a href="../course.php?id=<?= $lesson['course_id'] ?>" target="_blank" class="text-green-600 hover:text-green-900">
                                                    <i class="fas fa-eye" title="Voir le cours"></i>
                                                </a>
                                                <button onclick="deleteLesson(<?= $lesson['id'] ?>)" class="text-red-600 hover:text-red-900">
                                                    <i class="fas fa-trash" title="Supprimer"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Hidden form for actions -->
    <form id="action-form" method="POST" style="display: none;">
        <input type="hidden" name="action" id="action-input">
        <input type="hidden" name="lesson_id" id="lesson-id-input">
    </form>

    <script>
        function deleteLesson(lessonId) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cette leçon ? Le fichier associé sera également supprimé.')) {
                document.getElementById('action-input').value = 'delete';
                document.getElementById('lesson-id-input').value = lessonId;
                document.getElementById('action-form').submit();
            }
        }
    </script>
</body>
</html>
