<?php
/**
 * Homepage - AdrarPhysic E-Learning Platform
 *
 * Modern responsive homepage with hero section, featured courses, and latest content
 */

require_once 'config.php';

// Set page metadata
$pageTitle = 'Accueil';
$pageDescription = 'Plateforme d\'apprentissage en ligne pour la physique et la chimie. Cours, exercices et ressources pédagogiques pour tous les niveaux.';

// Get featured courses
try {
    $featuredCoursesStmt = executeQuery($pdo, "
        SELECT c.*, COUNT(l.id) as lesson_count
        FROM courses c
        LEFT JOIN lessons l ON c.id = l.course_id
        WHERE c.is_featured = 1
        GROUP BY c.id
        ORDER BY c.created_at DESC
        LIMIT 6
    ");
    $featuredCourses = $featuredCoursesStmt->fetchAll();
} catch (Exception $e) {
    $featuredCourses = [];
    error_log("Error fetching featured courses: " . $e->getMessage());
}

// Get latest courses
try {
    $latestCoursesStmt = executeQuery($pdo, "
        SELECT c.*, COUNT(l.id) as lesson_count
        FROM courses c
        LEFT JOIN lessons l ON c.id = l.course_id
        GROUP BY c.id
        ORDER BY c.created_at DESC
        LIMIT 8
    ");
    $latestCourses = $latestCoursesStmt->fetchAll();
} catch (Exception $e) {
    $latestCourses = [];
    error_log("Error fetching latest courses: " . $e->getMessage());
}

// Get course statistics
try {
    $statsStmt = executeQuery($pdo, "
        SELECT
            (SELECT COUNT(*) FROM courses) as total_courses,
            (SELECT COUNT(*) FROM lessons) as total_lessons,
            (SELECT COUNT(*) FROM users WHERE role = 'student') as total_students
    ");
    $stats = $statsStmt->fetch();
} catch (Exception $e) {
    $stats = ['total_courses' => 0, 'total_lessons' => 0, 'total_students' => 0];
    error_log("Error fetching statistics: " . $e->getMessage());
}

// Include header
include 'includes/header.php';
?>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div class="container mx-auto px-4 py-16">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h1 class="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
                        Apprenez la <span class="text-yellow-300">Physique</span> et la <span class="text-green-300">Chimie</span>
                    </h1>
                    <p class="text-xl mb-8 text-blue-100 leading-relaxed">
                        Découvrez notre plateforme d'apprentissage moderne avec des cours interactifs,
                        des exercices pratiques et des ressources pédagogiques pour tous les niveaux.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="courses.php" class="bg-yellow-500 hover:bg-yellow-600 text-gray-900 px-8 py-3 rounded-lg font-semibold transition-colors inline-flex items-center justify-center">
                            <i class="fas fa-play mr-2"></i>
                            Commencer maintenant
                        </a>
                        <a href="courses.php?featured=1" class="border-2 border-white hover:bg-white hover:text-blue-600 px-8 py-3 rounded-lg font-semibold transition-colors inline-flex items-center justify-center">
                            <i class="fas fa-star mr-2"></i>
                            Cours populaires
                        </a>
                    </div>
                </div>
                <div class="hidden lg:block">
                    <div class="relative">
                        <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                            <div class="grid grid-cols-2 gap-4">
                                <div class="bg-white/20 rounded-lg p-4 text-center">
                                    <i class="fas fa-atom text-3xl mb-2"></i>
                                    <h3 class="font-semibold">Physique</h3>
                                    <p class="text-sm text-blue-100">Mécanique, Électricité</p>
                                </div>
                                <div class="bg-white/20 rounded-lg p-4 text-center">
                                    <i class="fas fa-flask text-3xl mb-2"></i>
                                    <h3 class="font-semibold">Chimie</h3>
                                    <p class="text-sm text-blue-100">Organique, Minérale</p>
                                </div>
                                <div class="bg-white/20 rounded-lg p-4 text-center">
                                    <i class="fas fa-calculator text-3xl mb-2"></i>
                                    <h3 class="font-semibold">Exercices</h3>
                                    <p class="text-sm text-blue-100">Pratique guidée</p>
                                </div>
                                <div class="bg-white/20 rounded-lg p-4 text-center">
                                    <i class="fas fa-certificate text-3xl mb-2"></i>
                                    <h3 class="font-semibold">Examens</h3>
                                    <p class="text-sm text-blue-100">Préparation complète</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="bg-white py-12 border-b">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div class="p-6">
                    <div class="text-4xl font-bold text-blue-600 mb-2"><?= number_format($stats['total_courses']) ?>+</div>
                    <div class="text-gray-600 font-medium">Cours disponibles</div>
                </div>
                <div class="p-6">
                    <div class="text-4xl font-bold text-green-600 mb-2"><?= number_format($stats['total_lessons']) ?>+</div>
                    <div class="text-gray-600 font-medium">Leçons interactives</div>
                </div>
                <div class="p-6">
                    <div class="text-4xl font-bold text-purple-600 mb-2"><?= number_format($stats['total_students']) ?>+</div>
                    <div class="text-gray-600 font-medium">Étudiants inscrits</div>
                </div>
            </div>
        </div>
    </section>
    <!-- Featured Courses Section -->
    <?php if (!empty($featuredCourses)): ?>
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">
                    Cours en vedette
                </h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Découvrez nos cours les plus populaires et commencez votre apprentissage dès aujourd'hui
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php foreach ($featuredCourses as $course): ?>
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <div class="h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <i class="fas fa-<?= $course['category'] === 'Physique' ? 'atom' : 'flask' ?> text-white text-4xl"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-2">
                            <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                                <?= htmlspecialchars($course['category']) ?>
                            </span>
                            <span class="text-gray-500 text-sm"><?= htmlspecialchars($course['level']) ?></span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">
                            <?= htmlspecialchars($course['title']) ?>
                        </h3>
                        <p class="text-gray-600 mb-4 line-clamp-3">
                            <?= htmlspecialchars(substr($course['description'], 0, 120)) ?>...
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-gray-500 text-sm">
                                <i class="fas fa-book-open mr-1"></i>
                                <?= $course['lesson_count'] ?> leçons
                            </div>
                            <a href="course.php?id=<?= $course['id'] ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                Voir le cours
                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <div class="text-center mt-12">
                <a href="courses.php" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors inline-flex items-center">
                    Voir tous les cours
                    <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
        </div>
    </section>
    <?php endif; ?>
    <!-- Latest Courses Section -->
    <?php if (!empty($latestCourses)): ?>
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">
                    Derniers cours ajoutés
                </h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Restez à jour avec nos dernières ressources pédagogiques
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <?php foreach (array_slice($latestCourses, 0, 4) as $course): ?>
                <div class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300">
                    <div class="h-32 bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center">
                        <i class="fas fa-<?= $course['category'] === 'Physique' ? 'atom' : 'flask' ?> text-white text-2xl"></i>
                    </div>
                    <div class="p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2 py-1 rounded">
                                <?= htmlspecialchars($course['level']) ?>
                            </span>
                        </div>
                        <h3 class="font-semibold text-gray-800 mb-2 line-clamp-2">
                            <?= htmlspecialchars($course['title']) ?>
                        </h3>
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <span><i class="fas fa-book-open mr-1"></i><?= $course['lesson_count'] ?></span>
                            <a href="course.php?id=<?= $course['id'] ?>" class="text-blue-600 hover:text-blue-800 font-medium">
                                Voir →
                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Categories Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">
                    Explorez par niveau
                </h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Trouvez les ressources adaptées à votre niveau d'études
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Collège -->
                <div class="bg-white rounded-xl shadow-lg p-8 text-center hover:shadow-xl transition-shadow duration-300">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-school text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-2">Collège</h3>
                    <p class="text-gray-600 mb-4">1ère, 2ème et 3ème année collège</p>
                    <a href="courses.php?level=college" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        Explorer
                    </a>
                </div>

                <!-- Tronc Commun -->
                <div class="bg-white rounded-xl shadow-lg p-8 text-center hover:shadow-xl transition-shadow duration-300">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-graduation-cap text-green-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-2">Tronc Commun</h3>
                    <p class="text-gray-600 mb-4">Sciences et technologies</p>
                    <a href="courses.php?level=tc" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        Explorer
                    </a>
                </div>

                <!-- Baccalauréat -->
                <div class="bg-white rounded-xl shadow-lg p-8 text-center hover:shadow-xl transition-shadow duration-300">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-medal text-purple-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-2">Baccalauréat</h3>
                    <p class="text-gray-600 mb-4">1ère et 2ème année Bac</p>
                    <a href="courses.php?level=lycee" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        Explorer
                    </a>
                </div>
            </div>
        </div>
    </section>

<?php include 'includes/footer.php'; ?>