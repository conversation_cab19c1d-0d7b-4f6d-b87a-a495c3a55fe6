<?php
/**
 * Header Component
 * 
 * Contains the main navigation and header elements
 */

if (!isset($pdo)) {
    require_once 'config.php';
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? $pageTitle . ' - ' : '' ?><?= APP_NAME ?></title>
    <meta name="description" content="<?= isset($pageDescription) ? $pageDescription : 'Plateforme d\'apprentissage en ligne pour la physique et la chimie. Cours, exercices et ressources pédagogiques.' ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/custom.css">

    <style>
        body { font-family: 'Inter', sans-serif; }
        
        /* Custom dropdown styles */
        .dropdown {
            position: relative;
        }
        
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 250px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1000;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            top: 100%;
            left: 0;
        }
        
        .dropdown-content a {
            color: #374151;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            transition: background-color 0.3s;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .dropdown-content a:hover {
            background-color: #f9fafb;
        }
        
        .dropdown-content a:last-child {
            border-bottom: none;
        }
        
        .dropdown:hover .dropdown-content {
            display: block;
        }
        
        .dropdown > a::after {
            content: '▼';
            font-size: 10px;
            margin-left: 8px;
            transition: transform 0.3s;
        }
        
        .dropdown:hover > a::after {
            transform: rotate(180deg);
        }
        
        /* Mobile menu styles */
        .mobile-menu {
            display: none;
        }
        
        .mobile-menu.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .dropdown-content {
                position: static;
                display: block;
                box-shadow: none;
                border: none;
                background-color: #f9fafb;
                margin-left: 20px;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Top Navigation Bar -->
    <div class="bg-gray-800 text-white text-sm">
        <div class="container mx-auto px-4 py-2 flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <span><i class="fas fa-calendar-alt mr-1"></i> <?= date('l, F j, Y') ?></span>
            </div>
            <div class="flex items-center space-x-4">
                <?php if (isLoggedIn()): ?>
                    <span>Bonjour, <?= htmlspecialchars($_SESSION['user_name']) ?></span>
                    <?php if (isAdmin()): ?>
                        <a href="admin/dashboard.php" class="hover:text-blue-300">
                            <i class="fas fa-cog mr-1"></i> Admin
                        </a>
                    <?php endif; ?>
                    <a href="auth/logout.php" class="hover:text-blue-300">
                        <i class="fas fa-sign-out-alt mr-1"></i> Déconnexion
                    </a>
                <?php else: ?>
                    <a href="auth/login.php" class="hover:text-blue-300">
                        <i class="fas fa-sign-in-alt mr-1"></i> Connexion
                    </a>
                    <a href="auth/register.php" class="hover:text-blue-300">
                        <i class="fas fa-user-plus mr-1"></i> Inscription
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Main Header -->
    <header class="bg-white shadow-md">
        <div class="container mx-auto px-4">
            <!-- Logo and Search Bar -->
            <div class="flex items-center justify-between py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.php" class="flex items-center">
                        <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-atom text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-800">chimieMONDE</h1>
                            <p class="text-sm text-gray-600">Physique - Chimie</p>
                        </div>
                    </a>
                </div>

                <!-- Search Bar -->
                <div class="hidden md:flex flex-1 max-w-lg mx-8">
                    <form action="search.php" method="GET" class="w-full">
                        <div class="relative">
                            <input 
                                type="text" 
                                name="q" 
                                placeholder="Rechercher des cours, exercices..." 
                                class="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                value="<?= htmlspecialchars($_GET['q'] ?? '') ?>"
                            >
                            <button type="submit" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-blue-600">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Mobile Menu Button -->
                <button id="mobile-menu-btn" class="md:hidden text-gray-600 hover:text-blue-600">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>

            <!-- Main Navigation -->
            <nav class="border-t border-gray-200">
                <div class="hidden md:flex items-center space-x-8 py-4" id="main-nav">
                    <a href="index.php" class="text-gray-700 hover:text-blue-600 font-medium flex items-center">
                        <i class="fas fa-home mr-2"></i> Accueil
                    </a>

                    <!-- Collège Dropdown -->
                    <div class="dropdown">
                        <a href="courses.php?level=college" class="text-gray-700 hover:text-blue-600 font-medium cursor-pointer">
                            Collège
                        </a>
                        <div class="dropdown-content">
                            <a href="courses.php?level=1ac">1ère Année Collège</a>
                            <a href="courses.php?level=2ac">2ème Année Collège</a>
                            <a href="courses.php?level=3ac">3ème Année Collège</a>
                        </div>
                    </div>

                    <!-- Lycée Dropdown -->
                    <div class="dropdown">
                        <a href="courses.php?level=lycee" class="text-gray-700 hover:text-blue-600 font-medium cursor-pointer">
                            Lycée
                        </a>
                        <div class="dropdown-content">
                            <a href="courses.php?level=Tronc Commun">Tronc Commun</a>
                            <a href="courses.php?level=1ère Bac">1ère Bac</a>
                            <a href="courses.php?level=2ème Bac">2ème Bac</a>
                        </div>
                    </div>

                    <!-- Matières Dropdown -->
                    <div class="dropdown">
                        <a href="courses.php" class="text-gray-700 hover:text-blue-600 font-medium cursor-pointer">
                            Matières
                        </a>
                        <div class="dropdown-content">
                            <a href="courses.php?category=physique">Physique</a>
                            <a href="courses.php?category=chimie">Chimie</a>
                            <a href="courses.php?category=mathematiques">Mathématiques</a>
                        </div>
                    </div>

                    <a href="courses.php" class="text-gray-700 hover:text-blue-600 font-medium">
                        Tous les cours
                    </a>

                    <a href="contact.php" class="text-gray-700 hover:text-blue-600 font-medium">
                        Contact
                    </a>
                </div>

                <!-- Mobile Navigation -->
                <div id="mobile-menu" class="mobile-menu md:hidden py-4">
                    <div class="space-y-2">
                        <a href="index.php" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded">
                            <i class="fas fa-home mr-2"></i> Accueil
                        </a>
                        <a href="courses.php?level=college" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded">
                            Collège
                        </a>
                        <a href="courses.php?level=lycee" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded">
                            Lycée
                        </a>
                        <a href="courses.php" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded">
                            Tous les cours
                        </a>
                        <a href="contact.php" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded">
                            Contact
                        </a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('active');
            initMobileMenu()
        });
    </script>
