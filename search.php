<?php
/**
 * Search Results Page
 * 
 * Displays search results for courses and lessons
 */

require_once 'config.php';

// Get search query
$query = sanitizeInput($_GET['q'] ?? '');

// Set page metadata
$pageTitle = $query ? "Résultats pour \"$query\"" : 'Recherche';
$pageDescription = 'Recherchez des cours, leçons et ressources pédagogiques.';

$courses = [];
$lessons = [];
$totalResults = 0;

if (!empty($query)) {
    try {
        // Search in courses
        $coursesStmt = executeQuery($pdo, "
            SELECT c.*, COUNT(l.id) as lesson_count 
            FROM courses c 
            LEFT JOIN lessons l ON c.id = l.course_id 
            WHERE c.title LIKE ? OR c.description LIKE ? OR c.category LIKE ? OR c.level LIKE ?
            GROUP BY c.id 
            ORDER BY c.created_at DESC 
            LIMIT 20
        ", ["%$query%", "%$query%", "%$query%", "%$query%"]);
        $courses = $coursesStmt->fetchAll();

        // Search in lessons
        $lessonsStmt = executeQuery($pdo, "
            SELECT l.*, c.title as course_title, c.category, c.level 
            FROM lessons l 
            JOIN courses c ON l.course_id = c.id 
            WHERE l.title LIKE ? OR l.description LIKE ?
            ORDER BY l.created_at DESC 
            LIMIT 20
        ", ["%$query%", "%$query%"]);
        $lessons = $lessonsStmt->fetchAll();

        $totalResults = count($courses) + count($lessons);

    } catch (Exception $e) {
        error_log("Search error: " . $e->getMessage());
    }
}

// Include header
include 'includes/header.php';
?>

    <!-- Search Header -->
    <section class="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <h1 class="text-4xl lg:text-5xl font-bold mb-4">
                    <?= $query ? "Résultats pour \"" . htmlspecialchars($query) . "\"" : 'Recherche' ?>
                </h1>
                <?php if ($query): ?>
                    <p class="text-xl text-blue-100">
                        <?= $totalResults ?> résultat<?= $totalResults > 1 ? 's' : '' ?> trouvé<?= $totalResults > 1 ? 's' : '' ?>
                    </p>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Search Form -->
    <section class="bg-white py-8 border-b">
        <div class="container mx-auto px-4">
            <form method="GET" class="max-w-2xl mx-auto">
                <div class="relative">
                    <input 
                        type="text" 
                        name="q" 
                        placeholder="Rechercher des cours, leçons..." 
                        class="w-full px-6 py-4 pr-16 text-lg border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        value="<?= htmlspecialchars($query) ?>"
                        autofocus
                    >
                    <button type="submit" class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </section>

    <!-- Search Results -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <?php if (empty($query)): ?>
                <!-- Search Tips -->
                <div class="max-w-4xl mx-auto">
                    <div class="text-center mb-12">
                        <div class="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-search text-blue-600 text-3xl"></i>
                        </div>
                        <h2 class="text-3xl font-bold text-gray-800 mb-4">Que souhaitez-vous apprendre ?</h2>
                        <p class="text-xl text-gray-600">Recherchez parmi nos cours et ressources pédagogiques</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="bg-white rounded-lg p-6 text-center">
                            <i class="fas fa-atom text-blue-600 text-3xl mb-3"></i>
                            <h3 class="font-semibold text-gray-800 mb-2">Physique</h3>
                            <p class="text-gray-600 text-sm">Mécanique, électricité, optique...</p>
                        </div>
                        <div class="bg-white rounded-lg p-6 text-center">
                            <i class="fas fa-flask text-green-600 text-3xl mb-3"></i>
                            <h3 class="font-semibold text-gray-800 mb-2">Chimie</h3>
                            <p class="text-gray-600 text-sm">Chimie organique, minérale...</p>
                        </div>
                        <div class="bg-white rounded-lg p-6 text-center">
                            <i class="fas fa-calculator text-purple-600 text-3xl mb-3"></i>
                            <h3 class="font-semibold text-gray-800 mb-2">Exercices</h3>
                            <p class="text-gray-600 text-sm">Pratique et applications</p>
                        </div>
                    </div>
                </div>

            <?php elseif ($totalResults === 0): ?>
                <!-- No Results -->
                <div class="text-center py-16">
                    <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-search text-gray-400 text-3xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">Aucun résultat trouvé</h3>
                    <p class="text-gray-600 mb-6">Essayez avec d'autres mots-clés ou parcourez nos cours.</p>
                    <a href="courses.php" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        Voir tous les cours
                    </a>
                </div>

            <?php else: ?>
                <!-- Results -->
                <div class="max-w-4xl mx-auto">
                    <!-- Courses Results -->
                    <?php if (!empty($courses)): ?>
                        <div class="mb-12">
                            <h2 class="text-2xl font-bold text-gray-800 mb-6">
                                <i class="fas fa-book text-blue-600 mr-2"></i>
                                Cours (<?= count($courses) ?>)
                            </h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <?php foreach ($courses as $course): ?>
                                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                                        <div class="h-32 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                                            <i class="fas fa-<?= $course['category'] === 'Physique' ? 'atom' : 'flask' ?> text-white text-2xl"></i>
                                        </div>
                                        <div class="p-4">
                                            <div class="flex items-center justify-between mb-2">
                                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded">
                                                    <?= htmlspecialchars($course['category']) ?>
                                                </span>
                                                <span class="text-gray-500 text-sm"><?= htmlspecialchars($course['level']) ?></span>
                                            </div>
                                            <h3 class="font-semibold text-gray-800 mb-2">
                                                <a href="course.php?id=<?= $course['id'] ?>" class="hover:text-blue-600">
                                                    <?= htmlspecialchars($course['title']) ?>
                                                </a>
                                            </h3>
                                            <p class="text-gray-600 text-sm mb-3 line-clamp-2">
                                                <?= htmlspecialchars(substr($course['description'], 0, 100)) ?>...
                                            </p>
                                            <div class="flex items-center justify-between">
                                                <span class="text-gray-500 text-sm">
                                                    <i class="fas fa-book-open mr-1"></i><?= $course['lesson_count'] ?> leçons
                                                </span>
                                                <a href="course.php?id=<?= $course['id'] ?>" class="text-blue-600 hover:text-blue-800 font-medium text-sm">
                                                    Voir →
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Lessons Results -->
                    <?php if (!empty($lessons)): ?>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-800 mb-6">
                                <i class="fas fa-file-alt text-green-600 mr-2"></i>
                                Leçons (<?= count($lessons) ?>)
                            </h2>
                            <div class="space-y-4">
                                <?php foreach ($lessons as $lesson): ?>
                                    <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1">
                                                <div class="flex items-center mb-2">
                                                    <span class="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded mr-2">
                                                        <?= htmlspecialchars($lesson['category']) ?>
                                                    </span>
                                                    <span class="text-gray-500 text-sm"><?= htmlspecialchars($lesson['level']) ?></span>
                                                </div>
                                                <h3 class="font-semibold text-gray-800 mb-1">
                                                    <?= htmlspecialchars($lesson['title']) ?>
                                                </h3>
                                                <p class="text-gray-600 text-sm mb-2">
                                                    Cours: <a href="course.php?id=<?= $lesson['course_id'] ?>" class="text-blue-600 hover:text-blue-800">
                                                        <?= htmlspecialchars($lesson['course_title']) ?>
                                                    </a>
                                                </p>
                                                <?php if ($lesson['description']): ?>
                                                    <p class="text-gray-600 text-sm">
                                                        <?= htmlspecialchars($lesson['description']) ?>
                                                    </p>
                                                <?php endif; ?>
                                            </div>
                                            <div class="ml-4">
                                                <?php if ($lesson['file_path']): ?>
                                                    <a href="<?= htmlspecialchars($lesson['file_path']) ?>" 
                                                       target="_blank" 
                                                       class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors">
                                                        <i class="fas fa-external-link-alt mr-1"></i>
                                                        Ouvrir
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </section>

<?php include 'includes/footer.php'; ?>
