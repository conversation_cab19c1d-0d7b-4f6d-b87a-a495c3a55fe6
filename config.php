<?php
/**
 * Configuration file for E-Learning Platform
 * 
 * Contains all application settings and constants
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Application settings
define('APP_NAME', 'chimieMOND - E-Learning Platform');
define('APP_URL', 'http://localhost/hicham');
define('UPLOAD_DIR', __DIR__ . '/uploads/');
define('UPLOAD_URL', 'uploads/');

// File upload settings
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB
define('ALLOWED_FILE_TYPES', ['pdf', 'mp4', 'avi', 'mov', 'wmv', 'doc', 'docx', 'ppt', 'pptx']);

// Pagination settings
define('COURSES_PER_PAGE', 12);
define('POSTS_PER_PAGE', 10);

// Security settings
define('PASSWORD_MIN_LENGTH', 6);

// Include database connection
require_once 'db.php';

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

/**
 * Check if user is admin
 */
function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * Redirect to login if not authenticated
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: auth/login.php');
        exit;
    }
}

/**
 * Redirect to login if not admin
 */
function requireAdmin() {
    if (!isAdmin()) {
        
        header('Location: auth/login.php');
        exit;
    }
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Format file size
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * Get user data by ID
 */
function getUserById($pdo, $userId) {
    $stmt = executeQuery($pdo, "SELECT * FROM users WHERE id = ?", [$userId]);
    return $stmt->fetch();
}

/**
 * Get course data by ID
 */
function getCourseById($pdo, $courseId) {
    $stmt = executeQuery($pdo, "SELECT * FROM courses WHERE id = ?", [$courseId]);
    return $stmt->fetch();
}

/**
 * Get lessons for a course
 */
function getLessonsByCourse($pdo, $courseId) {
    $stmt = executeQuery($pdo, "SELECT * FROM lessons WHERE course_id = ? ORDER BY lesson_order ASC", [$courseId]);
    return $stmt->fetchAll();
}
