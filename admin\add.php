<?php
require_once '../db.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['name'] ?? '');
    $type = trim($_POST['lvl'] ?? '');
    $description = trim($_POST['dis'] ?? '');
    $lesson_id = intval($_POST['lesson_id'] ?? 0);
    $allowedTypes = ['cours', 'exercice', 'correction'];

    if ($lesson_id <= 0) {
        echo "Invalid lesson selected.";
        exit;
    }

    if (!in_array($type, $allowedTypes)) {
        echo "Invalid type. Allowed types: cours, exercice, correction.";
        exit;
    }

    if (!empty($_FILES['file']) && $_FILES['file']['error'] === 0) {
        $fileTmp = $_FILES['file']['tmp_name'];
        $fileName = basename($_FILES['file']['name']);
        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

        if ($fileExt !== 'pdf') {
            echo "Only PDF files are allowed.";
            exit;
        }

        $uploadDir = __DIR__ . '/uploads/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $newName = uniqid() . '_' . preg_replace('/[^a-zA-Z0-9_\.-]/', '_', $fileName);
        $filePath = $uploadDir . $newName;

        if (move_uploaded_file($fileTmp, $filePath)) {
            $dbFilePath = 'uploads/' . $newName;

            // Insert resource
            $stmt = $pdo->prepare("INSERT INTO resources (lesson_id, title, type, file_path) VALUES (?, ?, ?, ?)");
            $stmt->execute([$lesson_id, $title, $type, $dbFilePath]);

            // Update course description based on lesson's course
            $courseIdStmt = $pdo->prepare("SELECT course_id FROM lessons WHERE id = ?");
            $courseIdStmt->execute([$lesson_id]);
            $courseId = $courseIdStmt->fetchColumn();

            if ($courseId) {
                $updateDescStmt = $pdo->prepare("UPDATE courses SET description = ? WHERE id = ?");
                $updateDescStmt->execute([$description, $courseId]);
            }

            echo "Upload successful.";
        } else {
            echo "Failed to move the file.";
        }
    } else {
        echo "No file uploaded or error occurred.";
    }
}

// Fetch lessons for the select dropdown
$lessons = $pdo->query("SELECT id, title FROM lessons")->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html>
<head><title>Upload PDF</title></head>
<body>
    <form action="add.php" method="post" enctype="multipart/form-data">
        <label for="name">Title</label><br>
        <input type="text" name="name" required><br>

        <label for="lvl">Type (cours, exercice, correction)</label><br>
        <input type="text" name="lvl" required><br>

        <label for="dis">Description (will update course description)</label><br>
        <input type="text" name="dis"><br>

        <label for="lesson_id">Lesson</label><br>
        <select name="lesson_id" >
            <option value="">-- Select Lesson --</option>
            <?php foreach ($lessons as $lesson): ?>
                <option value="<?= htmlspecialchars($lesson['id']) ?>">
                    <?= htmlspecialchars($lesson['title']) ?>
                </option>
            <?php endforeach; ?>
        </select><br>

        <label for="file">PDF File</label><br>
        <input type="file" name="file" accept="application/pdf" required><br><br>

        <input type="submit" value="Upload">
    </form>
</body>
</html>
