<?php
/**
 * Course Details Page
 * 
 * Displays detailed information about a specific course and its lessons
 */

require_once 'config.php';

// Get course ID
$courseId = intval($_GET['id'] ?? 0);

if ($courseId <= 0) {
    header('Location: courses.php');
    exit;
}

try {
    // Get course details
    $course = getCourseById($pdo, $courseId);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }

    // Get lessons for this course
    $lessons = getLessonsByCourse($pdo, $courseId);

    // Get related courses (same category or level)
    $relatedStmt = executeQuery($pdo, "
        SELECT c.*, COUNT(l.id) as lesson_count 
        FROM courses c 
        LEFT JOIN lessons l ON c.id = l.course_id 
        WHERE (c.category = ? OR c.level = ?) AND c.id != ? 
        GROUP BY c.id 
        ORDER BY c.created_at DESC 
        LIMIT 4
    ", [$course['category'], $course['level'], $courseId]);
    $relatedCourses = $relatedStmt->fetchAll();

} catch (Exception $e) {
    error_log("Error fetching course details: " . $e->getMessage());
    header('Location: courses.php');
    exit;
}

// Set page metadata
$pageTitle = htmlspecialchars($course['title']);
$pageDescription = htmlspecialchars(substr($course['description'], 0, 160));

// Include header
include 'includes/header.php';
?>

    <!-- Course Header -->
    <section class="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
                <div class="lg:col-span-2">
                    <div class="flex items-center mb-4">
                        <span class="bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium mr-3">
                            <?= htmlspecialchars($course['category']) ?>
                        </span>
                        <span class="bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium">
                            <?= htmlspecialchars($course['level']) ?>
                        </span>
                        <?php if ($course['is_featured']): ?>
                            <span class="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-medium ml-3">
                                <i class="fas fa-star mr-1"></i>Vedette
                            </span>
                        <?php endif; ?>
                    </div>
                    <h1 class="text-4xl lg:text-5xl font-bold mb-4">
                        <?= htmlspecialchars($course['title']) ?>
                    </h1>
                    <p class="text-xl text-blue-100 leading-relaxed">
                        <?= htmlspecialchars($course['description']) ?>
                    </p>
                </div>
                <div class="lg:col-span-1">
                    <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6">
                        <div class="text-center">
                            <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-<?= $course['category'] === 'Physique' ? 'atom' : 'flask' ?> text-white text-3xl"></i>
                            </div>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-blue-100">Leçons:</span>
                                    <span class="font-semibold"><?= count($lessons) ?></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-blue-100">Niveau:</span>
                                    <span class="font-semibold"><?= htmlspecialchars($course['level']) ?></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-blue-100">Matière:</span>
                                    <span class="font-semibold"><?= htmlspecialchars($course['category']) ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Course Content -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2">
                    <!-- Course Description -->
                    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">
                            <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                            À propos de ce cours
                        </h2>
                        <div class="prose max-w-none">
                            <p class="text-gray-700 leading-relaxed">
                                <?= nl2br(htmlspecialchars($course['description'])) ?>
                            </p>
                        </div>
                    </div>

                    <!-- Lessons List -->
                    <?php if (!empty($lessons)): ?>
                        <div class="bg-white rounded-xl shadow-lg p-8">
                            <h2 class="text-2xl font-bold text-gray-800 mb-6">
                                <i class="fas fa-list text-blue-600 mr-2"></i>
                                Contenu du cours (<?= count($lessons) ?> leçons)
                            </h2>
                            <div class="space-y-4">
                                <?php foreach ($lessons as $index => $lesson): ?>
                                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-300">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold mr-3">
                                                    <?= $index + 1 ?>
                                                </div>
                                                <div>
                                                    <h3 class="font-semibold text-gray-800">
                                                        <?= htmlspecialchars($lesson['title']) ?>
                                                    </h3>
                                                    <?php if ($lesson['description']): ?>
                                                        <p class="text-gray-600 text-sm mt-1">
                                                            <?= htmlspecialchars($lesson['description']) ?>
                                                        </p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs font-medium">
                                                    <?= strtoupper($lesson['file_type']) ?>
                                                </span>
                                                <?php if ($lesson['file_path']): ?>
                                                    <a href="<?= htmlspecialchars($lesson['file_path']) ?>" 
                                                       target="_blank" 
                                                       class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors">
                                                        <i class="fas fa-external-link-alt mr-1"></i>
                                                        Ouvrir
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="bg-white rounded-xl shadow-lg p-8 text-center">
                            <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-book-open text-gray-400 text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-bold text-gray-800 mb-2">Aucune leçon disponible</h3>
                            <p class="text-gray-600">Les leçons pour ce cours seront bientôt disponibles.</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Course Actions -->
                    <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">Actions</h3>
                        <div class="space-y-3">
                            <a href="courses.php" class="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors text-center block">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Retour aux cours
                            </a>
                            <a href="courses.php?category=<?= urlencode($course['category']) ?>" 
                               class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-center block">
                                <i class="fas fa-list mr-2"></i>
                                Plus de <?= htmlspecialchars($course['category']) ?>
                            </a>
                        </div>
                    </div>

                    <!-- Related Courses -->
                    <?php if (!empty($relatedCourses)): ?>
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-bold text-gray-800 mb-4">
                                <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                                Cours similaires
                            </h3>
                            <div class="space-y-4">
                                <?php foreach ($relatedCourses as $relatedCourse): ?>
                                    <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow duration-300">
                                        <h4 class="font-semibold text-gray-800 text-sm mb-1">
                                            <a href="course.php?id=<?= $relatedCourse['id'] ?>" class="hover:text-blue-600">
                                                <?= htmlspecialchars($relatedCourse['title']) ?>
                                            </a>
                                        </h4>
                                        <div class="flex items-center justify-between text-xs text-gray-500">
                                            <span><?= htmlspecialchars($relatedCourse['level']) ?></span>
                                            <span><i class="fas fa-book-open mr-1"></i><?= $relatedCourse['lesson_count'] ?></span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

<?php include 'includes/footer.php'; ?>
