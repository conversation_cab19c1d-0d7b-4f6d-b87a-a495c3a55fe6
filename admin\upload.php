<?php
/**
 * Admin File Upload
 * 
 * Upload files and create lessons
 */

require_once '../config.php';

// Require admin access
requireAdmin();

$message = '';
$error = '';

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = sanitizeInput($_POST['title'] ?? '');
    $description = sanitizeInput($_POST['description'] ?? '');
    $courseId = intval($_POST['course_id'] ?? 0);
    
    if (empty($title) || $courseId <= 0) {
        $error = 'Veuillez remplir tous les champs obligatoires.';
    } elseif (!empty($_FILES['file']) && $_FILES['file']['error'] === 0) {
        $fileTmp = $_FILES['file']['tmp_name'];
        $fileName = basename($_FILES['file']['name']);
        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        
        // Check file type
        if (!in_array($fileExt, ALLOWED_FILE_TYPES)) {
            $error = 'Type de fichier non autorisé. Types autorisés: ' . implode(', ', ALLOWED_FILE_TYPES);
        } elseif ($_FILES['file']['size'] > MAX_FILE_SIZE) {
            $error = 'Le fichier est trop volumineux. Taille maximale: ' . formatFileSize(MAX_FILE_SIZE);
        } else {
            // Create upload directory if it doesn't exist
            if (!is_dir(UPLOAD_DIR)) {
                mkdir(UPLOAD_DIR, 0755, true);
            }
            
            // Generate unique filename
            $newName = uniqid() . '_' . preg_replace('/[^a-zA-Z0-9_\.-]/', '_', $fileName);
            $filePath = UPLOAD_DIR . $newName;
            $dbFilePath = UPLOAD_URL . $newName;
            
            if (move_uploaded_file($fileTmp, $filePath)) {
                try {
                    // Insert lesson
                    executeQuery($pdo, "
                        INSERT INTO lessons (course_id, title, description, file_path, file_type) 
                        VALUES (?, ?, ?, ?, ?)
                    ", [$courseId, $title, $description, $dbFilePath, $fileExt]);
                    
                    $message = 'Fichier uploadé et leçon créée avec succès!';
                    
                    // Clear form
                    $title = $description = '';
                    $courseId = 0;
                } catch (Exception $e) {
                    $error = 'Erreur lors de la sauvegarde: ' . $e->getMessage();
                    unlink($filePath); // Remove uploaded file on error
                }
            } else {
                $error = 'Erreur lors de l\'upload du fichier.';
            }
        }
    } else {
        $error = 'Veuillez sélectionner un fichier.';
    }
}

// Get courses for dropdown
try {
    $courses = executeQuery($pdo, "SELECT id, title, category, level FROM courses ORDER BY title")->fetchAll();
} catch (Exception $e) {
    $courses = [];
    $error = 'Erreur lors du chargement des cours.';
}

$pageTitle = 'Upload de fichiers - Administration';
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Admin Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="dashboard.php" class="text-blue-600 hover:text-blue-800 mr-4">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <h1 class="text-2xl font-bold text-gray-800">Upload de fichiers</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="../index.php" class="text-gray-600 hover:text-blue-600 transition-colors">
                        <i class="fas fa-home mr-1"></i> Site
                    </a>
                    <a href="../auth/logout.php" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i> Déconnexion
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar -->
        <aside class="w-64 bg-white shadow-sm min-h-screen">
            <nav class="p-6">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-tachometer-alt mr-3"></i>
                            Tableau de bord
                        </a>
                    </li>
                    <li>
                        <a href="add.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-book mr-3"></i>
                            Cours
                        </a>
                    </li>
                    <li>
                        <a href="lessons.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-file-alt mr-3"></i>
                            Leçons
                        </a>
                    </li>
                    <li>
                        <a href="users.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-users mr-3"></i>
                            Utilisateurs
                        </a>
                    </li>
                    <li>
                        <a href="upload.php" class="flex items-center px-4 py-2 text-blue-600 bg-blue-50 rounded-lg font-medium">
                            <i class="fas fa-upload mr-3"></i>
                            Upload fichiers
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6">
            <!-- Messages -->
            <?php if ($message): ?>
                <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
                    <p class="text-green-600"><?= htmlspecialchars($message) ?></p>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                    <p class="text-red-600"><?= htmlspecialchars($error) ?></p>
                </div>
            <?php endif; ?>

            <!-- Upload Form -->
            <div class="bg-white rounded-xl shadow-lg p-8">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">Uploader un nouveau fichier</h2>
                    <p class="text-gray-600">Ajoutez des ressources pédagogiques à vos cours.</p>
                </div>

                <form method="POST" enctype="multipart/form-data" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                Titre de la leçon *
                            </label>
                            <input 
                                type="text" 
                                id="title" 
                                name="title" 
                                required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                value="<?= htmlspecialchars($title ?? '') ?>"
                                placeholder="Ex: Introduction à la mécanique"
                            >
                        </div>

                        <div>
                            <label for="course_id" class="block text-sm font-medium text-gray-700 mb-2">
                                Cours *
                            </label>
                            <select 
                                id="course_id" 
                                name="course_id" 
                                required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                                <option value="">Sélectionner un cours</option>
                                <?php foreach ($courses as $course): ?>
                                    <option value="<?= $course['id'] ?>" <?= ($courseId ?? 0) == $course['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($course['title']) ?> (<?= htmlspecialchars($course['category']) ?> - <?= htmlspecialchars($course['level']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                            Description
                        </label>
                        <textarea 
                            id="description" 
                            name="description" 
                            rows="3" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Description de la leçon..."
                        ><?= htmlspecialchars($description ?? '') ?></textarea>
                    </div>

                    <div>
                        <label for="file" class="block text-sm font-medium text-gray-700 mb-2">
                            Fichier *
                        </label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <i class="fas fa-cloud-upload-alt text-gray-400 text-3xl mb-3"></i>
                                <div class="flex text-sm text-gray-600">
                                    <label for="file" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                        <span>Choisir un fichier</span>
                                        <input id="file" name="file" type="file" class="sr-only" required accept=".pdf,.doc,.docx,.ppt,.pptx,.mp4,.avi,.mov,.wmv">
                                    </label>
                                    <p class="pl-1">ou glisser-déposer</p>
                                </div>
                                <p class="text-xs text-gray-500">
                                    Types autorisés: <?= implode(', ', ALLOWED_FILE_TYPES) ?>
                                </p>
                                <p class="text-xs text-gray-500">
                                    Taille max: <?= formatFileSize(MAX_FILE_SIZE) ?>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                        <a href="dashboard.php" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Retour
                        </a>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-upload mr-2"></i>
                            Uploader le fichier
                        </button>
                    </div>
                </form>
            </div>
        </main>
    </div>

    <script>
        // File input enhancement
        document.getElementById('file').addEventListener('change', function(e) {
            const fileName = e.target.files[0]?.name;
            if (fileName) {
                const label = document.querySelector('label[for="file"] span');
                label.textContent = fileName;
            }
        });
    </script>
</body>
</html>
