<?php
/**
 * Setup Script
 * 
 * Initialize the database and create default data
 * Run this once after installation
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Read and execute SQL setup file
    $sql = file_get_contents('database_setup.sql');
    
    if ($sql === false) {
        throw new Exception('Could not read database_setup.sql file');
    }
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            $pdo->exec($statement);
        }
    }
    
    echo "✅ Database setup completed successfully!\n\n";
    echo "Default admin account:\n";
    echo "Email: <EMAIL>\n";
    echo "Password: admin123\n\n";
    echo "⚠️  Please change the default password after first login!\n\n";
    echo "🚀 Your AdrarPhysic e-learning platform is ready!\n";
    echo "Visit: http://localhost/hicham/\n";
    
} catch (Exception $e) {
    echo "❌ Setup failed: " . $e->getMessage() . "\n";
    echo "\nPlease check:\n";
    echo "1. MySQL server is running\n";
    echo "2. Database credentials are correct\n";
    echo "3. You have permission to create databases\n";
}
?>
