<?php
/**
 * Admin Users Management
 * 
 * View and manage users
 */

require_once '../config.php';

// Require admin access
requireAdmin();

$message = '';
$error = '';

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $userId = intval($_POST['user_id'] ?? 0);
    
    if ($action === 'delete' && $userId > 0) {
        try {
            // Don't allow deleting yourself
            if ($userId == $_SESSION['user_id']) {
                $error = 'Vous ne pouvez pas supprimer votre propre compte.';
            } else {
                executeQuery($pdo, "DELETE FROM users WHERE id = ?", [$userId]);
                $message = 'Utilisateur supprimé avec succès.';
            }
        } catch (Exception $e) {
            $error = 'Erreur lors de la suppression: ' . $e->getMessage();
        }
    } elseif ($action === 'toggle_role' && $userId > 0) {
        try {
            // Don't allow changing your own role
            if ($userId == $_SESSION['user_id']) {
                $error = 'Vous ne pouvez pas modifier votre propre rôle.';
            } else {
                $user = getUserById($pdo, $userId);
                $newRole = $user['role'] === 'admin' ? 'student' : 'admin';
                executeQuery($pdo, "UPDATE users SET role = ? WHERE id = ?", [$newRole, $userId]);
                $message = 'Rôle utilisateur modifié avec succès.';
            }
        } catch (Exception $e) {
            $error = 'Erreur lors de la modification: ' . $e->getMessage();
        }
    }
}

// Get all users
try {
    $users = executeQuery($pdo, "
        SELECT u.*, 
               COUNT(DISTINCT e.course_id) as enrolled_courses
        FROM users u 
        LEFT JOIN enrollments e ON u.id = e.user_id 
        GROUP BY u.id 
        ORDER BY u.created_at DESC
    ")->fetchAll();
} catch (Exception $e) {
    $users = [];
    $error = 'Erreur lors du chargement des utilisateurs.';
}

$pageTitle = 'Gestion des utilisateurs - Administration';
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Admin Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="dashboard.php" class="text-blue-600 hover:text-blue-800 mr-4">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <h1 class="text-2xl font-bold text-gray-800">Gestion des utilisateurs</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="../index.php" class="text-gray-600 hover:text-blue-600 transition-colors">
                        <i class="fas fa-home mr-1"></i> Site
                    </a>
                    <a href="../auth/logout.php" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i> Déconnexion
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar -->
        <aside class="w-64 bg-white shadow-sm min-h-screen">
            <nav class="p-6">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-tachometer-alt mr-3"></i>
                            Tableau de bord
                        </a>
                    </li>
                    <li>
                        <a href="add.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-book mr-3"></i>
                            Cours
                        </a>
                    </li>
                    <li>
                        <a href="lessons.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-file-alt mr-3"></i>
                            Leçons
                        </a>
                    </li>
                    <li>
                        <a href="users.php" class="flex items-center px-4 py-2 text-blue-600 bg-blue-50 rounded-lg font-medium">
                            <i class="fas fa-users mr-3"></i>
                            Utilisateurs
                        </a>
                    </li>
                    <li>
                        <a href="upload.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-upload mr-3"></i>
                            Upload fichiers
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6">
            <!-- Messages -->
            <?php if ($message): ?>
                <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
                    <p class="text-green-600"><?= htmlspecialchars($message) ?></p>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                    <p class="text-red-600"><?= htmlspecialchars($error) ?></p>
                </div>
            <?php endif; ?>

            <!-- Users Table -->
            <div class="bg-white rounded-xl shadow-lg">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-800">Tous les utilisateurs (<?= count($users) ?>)</h2>
                </div>
                
                <?php if (empty($users)): ?>
                    <div class="p-12 text-center">
                        <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-users text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">Aucun utilisateur</h3>
                        <p class="text-gray-600">Les utilisateurs apparaîtront ici une fois inscrits.</p>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utilisateur</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rôle</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cours inscrits</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Inscription</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($users as $user): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                                                    <i class="fas fa-user text-gray-500"></i>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">
                                                        <?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?>
                                                    </div>
                                                    <div class="text-sm text-gray-500">
                                                        @<?= htmlspecialchars($user['username']) ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?= htmlspecialchars($user['email']) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $user['role'] === 'admin' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800' ?>">
                                                <?= ucfirst($user['role']) ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?= $user['enrolled_courses'] ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?= date('d/m/Y', strtotime($user['created_at'])) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                <div class="flex space-x-2">
                                                    <button onclick="toggleRole(<?= $user['id'] ?>, '<?= $user['role'] ?>')" 
                                                            class="text-indigo-600 hover:text-indigo-900">
                                                        <i class="fas fa-user-cog" title="Changer le rôle"></i>
                                                    </button>
                                                    <button onclick="deleteUser(<?= $user['id'] ?>)" 
                                                            class="text-red-600 hover:text-red-900">
                                                        <i class="fas fa-trash" title="Supprimer"></i>
                                                    </button>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-gray-400 text-xs">Vous</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Hidden forms for actions -->
    <form id="action-form" method="POST" style="display: none;">
        <input type="hidden" name="action" id="action-input">
        <input type="hidden" name="user_id" id="user-id-input">
    </form>

    <script>
        function deleteUser(userId) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ? Cette action est irréversible.')) {
                document.getElementById('action-input').value = 'delete';
                document.getElementById('user-id-input').value = userId;
                document.getElementById('action-form').submit();
            }
        }

        function toggleRole(userId, currentRole) {
            const newRole = currentRole === 'admin' ? 'étudiant' : 'administrateur';
            if (confirm(`Êtes-vous sûr de vouloir changer le rôle de cet utilisateur en ${newRole} ?`)) {
                document.getElementById('action-input').value = 'toggle_role';
                document.getElementById('user-id-input').value = userId;
                document.getElementById('action-form').submit();
            }
        }
    </script>
</body>
</html>
