-- Create the elearning database
CREATE DATABASE IF NOT EXISTS elearning CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE elearning;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON><PERSON><PERSON>(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'student') DEFAULT 'student',
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Courses table
CREATE TABLE IF NOT EXISTS courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    thumbnail VARCHAR(255),
    category VARCHAR(100),
    level VARCHAR(50),
    instructor_id INT,
    is_featured BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Lessons table
CREATE TABLE IF NOT EXISTS lessons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    file_path VARCHAR(255),
    file_type ENUM('pdf', 'video', 'document') DEFAULT 'pdf',
    lesson_order INT DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
);

-- User enrollments table
CREATE TABLE IF NOT EXISTS enrollments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    enrolled_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    progress DECIMAL(5,2) DEFAULT 0.00,
    UNIQUE KEY unique_enrollment (user_id, course_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
);

-- Categories table for better organization
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    parent_id INT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password, role, first_name, last_name) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'Admin', 'User')
ON DUPLICATE KEY UPDATE username = username;

-- Insert sample categories
INSERT INTO categories (name, description) VALUES 
('Physique', 'Cours de physique pour tous les niveaux'),
('Chimie', 'Cours de chimie pour tous les niveaux'),
('Mathématiques', 'Cours de mathématiques'),
('Collège', 'Cours niveau collège'),
('Lycée', 'Cours niveau lycée'),
('Tronc Commun', 'Cours tronc commun scientifique'),
('1ère Bac', 'Première année baccalauréat'),
('2ème Bac', 'Deuxième année baccalauréat')
ON DUPLICATE KEY UPDATE name = name;

-- Insert sample courses
INSERT INTO courses (title, description, category, level, thumbnail, is_featured) VALUES 
('Physique - Mécanique', 'Cours complet de mécanique pour le tronc commun', 'Physique', 'Tronc Commun', 'uploads/physics-mechanics.jpg', TRUE),
('Chimie - Atomes et Molécules', 'Introduction à la chimie atomique et moléculaire', 'Chimie', 'Tronc Commun', 'uploads/chemistry-atoms.jpg', TRUE),
('Électricité et Magnétisme', 'Cours d\'électricité et magnétisme pour 1ère Bac', 'Physique', '1ère Bac', 'uploads/electricity.jpg', FALSE),
('Chimie Organique', 'Bases de la chimie organique pour 2ème Bac', 'Chimie', '2ème Bac', 'uploads/organic-chemistry.jpg', TRUE)
ON DUPLICATE KEY UPDATE title = title;

-- Insert sample lessons
INSERT INTO lessons (course_id, title, description, file_path, file_type, lesson_order) VALUES 
(1, 'Introduction à la Mécanique', 'Les bases de la mécanique classique', 'uploads/lesson1.pdf', 'pdf', 1),
(1, 'Les Forces', 'Étude des forces et leurs applications', 'uploads/lesson2.pdf', 'pdf', 2),
(2, 'Structure de l\'Atome', 'Découverte de la structure atomique', 'uploads/lesson3.pdf', 'pdf', 1),
(2, 'Liaisons Chimiques', 'Types de liaisons entre atomes', 'uploads/lesson4.pdf', 'pdf', 2)
ON DUPLICATE KEY UPDATE title = title;
