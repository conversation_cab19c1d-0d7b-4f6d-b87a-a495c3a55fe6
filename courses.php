<?php
/**
 * Courses Listing Page
 * 
 * Displays all courses with search and filter functionality
 */

require_once 'config.php';

// Set page metadata
$pageTitle = 'Tous les cours';
$pageDescription = 'Découvrez tous nos cours de physique et chimie pour tous les niveaux. Recherchez et filtrez par matière, niveau et type.';

// Get filter parameters
$search = sanitizeInput($_GET['q'] ?? '');
$category = sanitizeInput($_GET['category'] ?? '');
$level = sanitizeInput($_GET['level'] ?? '');
$featured = isset($_GET['featured']) ? 1 : 0;

// Pagination
$page = max(1, intval($_GET['page'] ?? 1));
$limit = COURSES_PER_PAGE;
$offset = ($page - 1) * $limit;

// Build WHERE clause
$whereConditions = [];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(c.title LIKE ? OR c.description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($category)) {
    $whereConditions[] = "c.category = ?";
    $params[] = $category;
}

if (!empty($level)) {
    $whereConditions[] = "c.level = ?";
    $params[] = $level;
}

if ($featured) {
    $whereConditions[] = "c.is_featured = 1";
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

try {
    // Get total count for pagination
    $countSql = "SELECT COUNT(*) FROM courses c $whereClause";
    $countStmt = executeQuery($pdo, $countSql, $params);
    $totalCourses = $countStmt->fetchColumn();
    $totalPages = ceil($totalCourses / $limit);

    // Get courses
    $sql = "
        SELECT c.*, COUNT(l.id) as lesson_count 
        FROM courses c 
        LEFT JOIN lessons l ON c.id = l.course_id 
        $whereClause
        GROUP BY c.id 
        ORDER BY c.created_at DESC 
        LIMIT $limit OFFSET $offset
    ";
    $stmt = executeQuery($pdo, $sql, $params);
    $courses = $stmt->fetchAll();

    // Get categories for filter
    $categoriesStmt = executeQuery($pdo, "SELECT DISTINCT category FROM courses WHERE category IS NOT NULL ORDER BY category");
    $categories = $categoriesStmt->fetchAll(PDO::FETCH_COLUMN);

    // Get levels for filter
    $levelsStmt = executeQuery($pdo, "SELECT DISTINCT level FROM courses WHERE level IS NOT NULL ORDER BY level");
    $levels = $levelsStmt->fetchAll(PDO::FETCH_COLUMN);

} catch (Exception $e) {
    $courses = [];
    $categories = [];
    $levels = [];
    $totalCourses = 0;
    $totalPages = 0;
    error_log("Error fetching courses: " . $e->getMessage());
}

// Include header
include 'includes/header.php';
?>

    <!-- Page Header -->
    <section class="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <h1 class="text-4xl lg:text-5xl font-bold mb-4">
                    <?= $featured ? 'Cours en vedette' : 'Tous les cours' ?>
                </h1>
                <p class="text-xl text-blue-100 max-w-2xl mx-auto">
                    <?= $totalCourses ?> cours disponibles pour votre apprentissage
                </p>
            </div>
        </div>
    </section>

    <!-- Search and Filters -->
    <section class="bg-white py-8 border-b">
        <div class="container mx-auto px-4">
            <form method="GET" class="space-y-4 lg:space-y-0 lg:flex lg:items-center lg:space-x-4">
                <!-- Search -->
                <div class="flex-1">
                    <div class="relative">
                        <input 
                            type="text" 
                            name="q" 
                            placeholder="Rechercher des cours..." 
                            class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            value="<?= htmlspecialchars($search) ?>"
                        >
                        <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-blue-600">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- Category Filter -->
                <div class="lg:w-48">
                    <select name="category" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Toutes les matières</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?= htmlspecialchars($cat) ?>" <?= $category === $cat ? 'selected' : '' ?>>
                                <?= htmlspecialchars($cat) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Level Filter -->
                <div class="lg:w-48">
                    <select name="level" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Tous les niveaux</option>
                        <?php foreach ($levels as $lvl): ?>
                            <option value="<?= htmlspecialchars($lvl) ?>" <?= $level === $lvl ? 'selected' : '' ?>>
                                <?= htmlspecialchars($lvl) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Submit Button -->
                <button type="submit" class="w-full lg:w-auto bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-filter mr-2"></i>
                    Filtrer
                </button>

                <!-- Clear Filters -->
                <?php if ($search || $category || $level || $featured): ?>
                    <a href="courses.php" class="w-full lg:w-auto bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors text-center inline-block">
                        <i class="fas fa-times mr-2"></i>
                        Effacer
                    </a>
                <?php endif; ?>
            </form>
        </div>
    </section>

    <!-- Courses Grid -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <?php if (empty($courses)): ?>
                <div class="text-center py-16">
                    <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-search text-gray-400 text-3xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">Aucun cours trouvé</h3>
                    <p class="text-gray-600 mb-6">Essayez de modifier vos critères de recherche.</p>
                    <a href="courses.php" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        Voir tous les cours
                    </a>
                </div>
            <?php else: ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <?php foreach ($courses as $course): ?>
                        <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                            <div class="h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center relative">
                                <i class="fas fa-<?= $course['category'] === 'Physique' ? 'atom' : 'flask' ?> text-white text-4xl"></i>
                                <?php if ($course['is_featured']): ?>
                                    <div class="absolute top-3 right-3 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                        <i class="fas fa-star mr-1"></i>Vedette
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                                        <?= htmlspecialchars($course['category']) ?>
                                    </span>
                                    <span class="text-gray-500 text-sm"><?= htmlspecialchars($course['level']) ?></span>
                                </div>
                                <h3 class="text-lg font-bold text-gray-800 mb-2 line-clamp-2">
                                    <?= htmlspecialchars($course['title']) ?>
                                </h3>
                                <p class="text-gray-600 mb-4 text-sm line-clamp-3">
                                    <?= htmlspecialchars(substr($course['description'], 0, 100)) ?>...
                                </p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center text-gray-500 text-sm">
                                        <i class="fas fa-book-open mr-1"></i>
                                        <?= $course['lesson_count'] ?> leçons
                                    </div>
                                    <a href="course.php?id=<?= $course['id'] ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                        Voir le cours
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="mt-12 flex justify-center">
                        <nav class="flex items-center space-x-2">
                            <?php if ($page > 1): ?>
                                <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>" 
                                   class="px-3 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <a href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>" 
                                   class="px-4 py-2 <?= $i === $page ? 'bg-blue-600 text-white' : 'bg-white border border-gray-300 hover:bg-gray-50' ?> rounded-md transition-colors">
                                    <?= $i ?>
                                </a>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>" 
                                   class="px-3 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </section>

<?php include 'includes/footer.php'; ?>
