# AdrarPhysic - E-Learning Platform

A modern, responsive educational website for physics and chemistry learning, inspired by adrarphysic.fr.

## Features

### 🎓 Student Features
- **Modern Homepage** with hero section and featured courses
- **Course Catalog** with search and filter functionality
- **Course Details** with lessons and downloadable resources
- **User Authentication** with secure login/register system
- **Responsive Design** that works on all devices
- **Search Functionality** to find courses and lessons quickly

### 👨‍💼 Admin Features
- **Admin Dashboard** with statistics and overview
- **Course Management** - Add, edit, delete courses
- **Lesson Management** - Upload files and create lessons
- **User Management** - View and manage registered users
- **File Upload System** with support for PDFs, videos, and documents

## Technology Stack

- **Frontend**: HTML5, Tailwind CSS, JavaScript
- **Backend**: PHP 7.4+ with PDO
- **Database**: MySQL 5.7+
- **Icons**: Font Awesome 6
- **Fonts**: Google Fonts (Inter)

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache/Nginx web server
- Composer (optional)

### Setup Instructions

1. **Clone or download** the project to your web server directory
   ```bash
   # If using git
   git clone <repository-url> adrarphysic
   cd adrarphysic
   ```

2. **Database Setup**
   - Create a MySQL database named `elearning`
   - Import the database structure:
   ```bash
   mysql -u root -p elearning < database_setup.sql
   ```
   - Or run the SQL commands from `database_setup.sql` in your MySQL client

3. **Configuration**
   - Update database credentials in `db.php` if needed
   - Ensure the `uploads/` directory has write permissions:
   ```bash
   chmod 755 uploads/
   ```

4. **Web Server Configuration**
   - Ensure mod_rewrite is enabled (for Apache)
   - Point your web server to the project directory
   - The `.htaccess` file contains necessary configurations

### Default Admin Account
- **Email**: <EMAIL>
- **Password**: admin123

**⚠️ Important**: Change the default admin password after first login!

## File Structure

```
adrarphysic/
├── admin/                  # Admin panel
│   ├── dashboard.php      # Admin dashboard
│   ├── add.php           # Course management (renamed from original)
│   ├── upload.php        # File upload
│   └── users.php         # User management
├── auth/                  # Authentication
│   ├── login.php         # Login page
│   ├── register.php      # Registration page
│   └── logout.php        # Logout script
├── assets/               # Static assets
│   ├── css/
│   │   └── custom.css    # Custom styles
│   └── js/
│       └── main.js       # JavaScript functionality
├── includes/             # Shared components
│   ├── header.php        # Site header
│   └── footer.php        # Site footer
├── uploads/              # Uploaded files
├── index.php             # Homepage
├── courses.php           # Course listing
├── course.php            # Course details
├── search.php            # Search results
├── contact.php           # Contact page
├── config.php            # Application configuration
├── db.php                # Database connection
├── database_setup.sql    # Database structure
├── .htaccess            # Apache configuration
└── README.md            # This file
```

## Database Schema

### Tables
- **users**: User accounts with roles (admin/student)
- **courses**: Course information and metadata
- **lessons**: Individual lessons linked to courses
- **enrollments**: User course enrollments (for future use)
- **categories**: Course categories for organization

### Key Features
- Foreign key constraints for data integrity
- UTF-8 support for international content
- Timestamps for audit trails
- Proper indexing for performance

## Usage

### For Students
1. Visit the homepage
2. Browse courses by category or level
3. Register for a free account
4. Access course materials and lessons

### For Administrators
1. Login with admin credentials
2. Access the admin dashboard
3. Add new courses and upload lessons
4. Manage users and content

## Security Features

- **Password Hashing**: Using PHP's `password_hash()` function
- **SQL Injection Prevention**: PDO prepared statements
- **XSS Protection**: Input sanitization and output escaping
- **CSRF Protection**: Token-based form protection
- **Session Security**: Secure session configuration
- **File Upload Security**: Type and size validation

## Performance Optimizations

- **Gzip Compression**: Enabled via .htaccess
- **Browser Caching**: Static assets cached for 30 days
- **Lazy Loading**: Images loaded on demand
- **Minified Assets**: CSS and JS optimization
- **Database Optimization**: Proper indexing and queries

## SEO Features

- **Meta Tags**: Proper title and description tags
- **Semantic HTML**: Structured markup
- **Open Graph**: Social media sharing optimization
- **Sitemap Ready**: Structure for XML sitemap generation
- **Mobile Friendly**: Responsive design for all devices

## Customization

### Styling
- Modify `assets/css/custom.css` for custom styles
- Tailwind CSS classes can be customized
- Color scheme can be changed in the CSS variables

### Content
- Update site name and branding in `config.php`
- Modify navigation menus in `includes/header.php`
- Customize footer content in `includes/footer.php`

### Functionality
- Add new course categories in the database
- Extend user roles and permissions
- Add new file types to the upload system

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `db.php`
   - Ensure MySQL service is running
   - Verify database exists and is accessible

2. **File Upload Issues**
   - Check `uploads/` directory permissions
   - Verify PHP upload settings in `php.ini`
   - Ensure file size limits are appropriate

3. **Permission Errors**
   - Set proper file permissions (755 for directories, 644 for files)
   - Ensure web server has read/write access to uploads directory

4. **Styling Issues**
   - Verify Tailwind CSS is loading from CDN
   - Check for JavaScript errors in browser console
   - Ensure Font Awesome icons are loading

## Support

For support and questions:
- Check the documentation in this README
- Review the code comments for implementation details
- Contact the development team

## License

This project is created for educational purposes. Please respect copyright and licensing terms when using or modifying the code.

---

**Built with ❤️ for education**
